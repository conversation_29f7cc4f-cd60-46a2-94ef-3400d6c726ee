#!/usr/bin/env python3
"""
Forex Factory Calendar Scraper using Selenium
Alternative scraper using browser automation for JavaScript-heavy content
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from datetime import datetime, timedelta
import pytz
import re
import time
from typing import List, Dict, Optional


class ForexFactorySeleniumScraper:
    def __init__(self, headless: bool = True):
        self.base_url = "https://www.forexfactory.com"
        self.calendar_url = f"{self.base_url}/calendar"
        self.headless = headless
        self.driver = None

    def setup_driver(self):
        """Setup Chrome WebDriver"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument("--headless")
        
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            return True
        except Exception as e:
            print(f"Error setting up Chrome driver: {e}")
            print("Make sure ChromeDriver is installed and in PATH")
            return False

    def get_local_timezone(self) -> pytz.timezone:
        """Get the local timezone"""
        try:
            import tzlocal
            return tzlocal.get_localzone()
        except ImportError:
            print("Warning: tzlocal not available, using UTC. Install with: pip install tzlocal")
            return pytz.UTC

    def parse_time(self, time_str: str, date_str: str = None) -> Optional[datetime]:
        """Parse time string from Forex Factory format"""
        if not time_str or time_str.strip() == "":
            return None
            
        try:
            time_str = time_str.strip()
            
            # Handle "All Day" events
            if "all day" in time_str.lower():
                return None
            
            # Remove any extra whitespace
            time_str = re.sub(r'\s+', ' ', time_str)
            
            # Try to parse the time
            if 'am' in time_str.lower() or 'pm' in time_str.lower():
                # 12-hour format
                time_str_clean = re.sub(r'[^\d:apm]', '', time_str.lower())
                time_obj = datetime.strptime(time_str_clean, '%I:%M%p')
            else:
                # 24-hour format
                time_str_clean = re.sub(r'[^\d:]', '', time_str)
                time_obj = datetime.strptime(time_str_clean, '%H:%M')
            
            # Use today's date if no date provided
            today = datetime.now().date()
            combined = datetime.combine(today, time_obj.time())
            
            # Assume the time is in EST (Forex Factory's timezone)
            est = pytz.timezone('US/Eastern')
            localized = est.localize(combined)
            
            return localized
            
        except Exception as e:
            print(f"Error parsing time '{time_str}': {e}")
            return None

    def is_usd_related(self, currency: str, title: str) -> bool:
        """Check if the news event is USD related"""
        if not currency:
            return False
            
        currency = currency.upper().strip()
        
        # Direct USD currency
        if currency == 'USD':
            return True
            
        # Check for USD in currency pairs
        if 'USD' in currency:
            return True
            
        # Check title for USD-related keywords
        usd_keywords = ['dollar', 'fed', 'federal reserve', 'fomc', 'us ', 'united states', 'america']
        title_lower = title.lower()
        
        return any(keyword in title_lower for keyword in usd_keywords)

    def is_high_impact(self, impact_element) -> bool:
        """Check if the event is high impact"""
        if not impact_element:
            return False
            
        try:
            # Check for high impact indicators in class names or attributes
            classes = impact_element.get_attribute('class') or ''
            title = impact_element.get_attribute('title') or ''
            
            # Look for red/high impact indicators
            high_impact_indicators = ['high', 'red', 'impact-3', 'ff_impact_red']
            
            return any(indicator in classes.lower() for indicator in high_impact_indicators) or \
                   any(indicator in title.lower() for indicator in high_impact_indicators)
                   
        except Exception:
            return False

    def scrape_calendar(self) -> List[Dict]:
        """Scrape the Forex Factory calendar using Selenium"""
        if not self.setup_driver():
            return []
            
        try:
            print("Loading Forex Factory calendar...")
            self.driver.get(self.calendar_url)
            
            # Wait for the calendar to load
            wait = WebDriverWait(self.driver, 20)
            
            # Try different selectors for the calendar
            calendar_selectors = [
                "table.calendar__table",
                "table#calendar",
                ".calendar__table",
                "[class*='calendar']"
            ]
            
            calendar_element = None
            for selector in calendar_selectors:
                try:
                    calendar_element = wait.until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    print(f"Found calendar using selector: {selector}")
                    break
                except TimeoutException:
                    continue
            
            if not calendar_element:
                print("Could not find calendar table. Trying to find event rows directly...")
                # Try to find event rows directly
                event_rows = self.driver.find_elements(By.CSS_SELECTOR, "tr[class*='calendar__row'], .calendar__row")
                if event_rows:
                    return self._parse_event_rows(event_rows)
                else:
                    print("No calendar events found")
                    return []
            
            # Find all event rows
            event_rows = calendar_element.find_elements(By.TAG_NAME, "tr")
            return self._parse_event_rows(event_rows)
            
        except Exception as e:
            print(f"Error scraping calendar: {e}")
            return []
        finally:
            if self.driver:
                self.driver.quit()

    def _parse_event_rows(self, rows) -> List[Dict]:
        """Parse events from table rows"""
        events = []
        current_date = None
        
        for row in rows:
            try:
                # Check if this is a date row
                date_cells = row.find_elements(By.CSS_SELECTOR, "td[class*='calendar__date'], .calendar__date")
                if date_cells:
                    date_text = date_cells[0].text.strip()
                    if date_text:
                        current_date = date_text
                    continue
                
                # Check if this is an event row
                time_cells = row.find_elements(By.CSS_SELECTOR, "td[class*='calendar__time'], .calendar__time")
                if not time_cells:
                    continue
                
                event = self._parse_event_row(row, current_date)
                if event:
                    events.append(event)
                    
            except Exception as e:
                print(f"Error parsing row: {e}")
                continue
        
        return events

    def _parse_event_row(self, row, current_date=None) -> Optional[Dict]:
        """Parse a single event row"""
        try:
            # Extract time
            time_cells = row.find_elements(By.CSS_SELECTOR, "td[class*='calendar__time'], .calendar__time")
            time_str = time_cells[0].text.strip() if time_cells else ""
            
            # Extract currency
            currency_cells = row.find_elements(By.CSS_SELECTOR, "td[class*='calendar__currency'], .calendar__currency")
            currency = currency_cells[0].text.strip() if currency_cells else ""
            
            # Extract impact
            impact_cells = row.find_elements(By.CSS_SELECTOR, "td[class*='calendar__impact'], .calendar__impact")
            impact_element = None
            if impact_cells:
                impact_spans = impact_cells[0].find_elements(By.TAG_NAME, "span")
                if impact_spans:
                    impact_element = impact_spans[0]
            
            # Extract event title
            event_cells = row.find_elements(By.CSS_SELECTOR, "td[class*='calendar__event'], .calendar__event")
            title = event_cells[0].text.strip() if event_cells else ""
            
            # Check if this is USD-related and high impact
            if not self.is_usd_related(currency, title):
                return None
                
            if not self.is_high_impact(impact_element):
                return None
            
            # Parse the time
            event_time = self.parse_time(time_str, current_date)
            
            return {
                'time': event_time,
                'currency': currency,
                'title': title,
                'impact': 'High',
                'raw_time': time_str,
                'date': current_date
            }
            
        except Exception as e:
            print(f"Error parsing event row: {e}")
            return None

    def filter_next_3_hours(self, events: List[Dict]) -> List[Dict]:
        """Filter events that occur within the next 3 hours"""
        local_tz = self.get_local_timezone()
        now = datetime.now(local_tz)
        three_hours_later = now + timedelta(hours=3)
        
        filtered_events = []
        
        for event in events:
            if event['time']:
                # Convert to local timezone
                local_time = event['time'].astimezone(local_tz)
                
                if now <= local_time <= three_hours_later:
                    event['local_time'] = local_time
                    filtered_events.append(event)
        
        return filtered_events

    def run(self) -> List[Dict]:
        """Main method to run the scraper"""
        print("Starting Forex Factory Selenium scraper...")
        
        # Scrape calendar
        all_events = self.scrape_calendar()
        print(f"Found {len(all_events)} total USD high-impact events")
        
        # Filter for next 3 hours
        upcoming_events = self.filter_next_3_hours(all_events)
        print(f"Found {len(upcoming_events)} events in the next 3 hours")
        
        return upcoming_events


def main():
    """Main function"""
    scraper = ForexFactorySeleniumScraper(headless=True)
    events = scraper.run()
    
    if events:
        print("\n" + "="*60)
        print("HIGH IMPACT USD NEWS - NEXT 3 HOURS")
        print("="*60)
        
        for event in events:
            local_time = event.get('local_time', event['time'])
            print(f"\nTime: {local_time.strftime('%Y-%m-%d %H:%M %Z')}")
            print(f"Currency: {event['currency']}")
            print(f"Event: {event['title']}")
            print(f"Impact: {event['impact']}")
            print("-" * 40)
    else:
        print("\nNo high impact USD news events found in the next 3 hours.")


if __name__ == "__main__":
    main()
