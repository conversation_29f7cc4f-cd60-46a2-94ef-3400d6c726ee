# Forex Factory Calendar Scraper

This script scrapes the Forex Factory calendar (https://www.forexfactory.com/calendar) to find high impact USD news events occurring within the next 3 hours in your local timezone.

## Features

- Scrapes high impact USD-related news events
- Filters events for the next 3 hours in local time
- Supports both requests/BeautifulSoup and Selenium approaches
- Automatic timezone conversion
- Clean, formatted output

## Installation

1. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **For Selenium support (recommended), install ChromeDriver:**
   
   **Windows:**
   - Download ChromeDriver from https://chromedriver.chromium.org/
   - Extract and add to PATH, or place in the same directory as the script
   
   **macOS:**
   ```bash
   brew install chromedriver
   ```
   
   **Linux:**
   ```bash
   sudo apt-get install chromium-chromedriver
   ```

## Usage

### Quick Start
```bash
python run_scraper.py
```

### Command Line Options
```bash
# Use only the requests approach (faster, but may not work if site uses JavaScript)
python run_scraper.py --requests-only

# Use only Selenium approach (more reliable, but requires ChromeDriver)
python run_scraper.py --selenium-only

# Run individual scrapers directly
python forex_factory_scraper.py        # Requests/BeautifulSoup approach
python forex_factory_selenium.py       # Selenium approach
```

## How It Works

1. **Dual Approach**: The script tries two different scraping methods:
   - **Requests + BeautifulSoup**: Fast, lightweight approach
   - **Selenium**: Browser automation for JavaScript-heavy content

2. **USD Filtering**: Events are filtered to include only USD-related news by checking:
   - Currency field contains "USD"
   - Event title contains USD-related keywords (Fed, Federal Reserve, etc.)

3. **Impact Filtering**: Only high impact events (red icons) are included

4. **Time Filtering**: Events are filtered to show only those occurring within the next 3 hours

5. **Timezone Handling**: All times are converted to your local timezone

## Output Example

```
HIGH IMPACT USD NEWS - NEXT 3 HOURS
======================================================================

1. FOMC Meeting Minutes
   Time: 2024-01-15 14:00 EST
   Currency: USD
   Impact: High
   Time until: 2h 30m
--------------------------------------------------

2. Non-Farm Payrolls
   Time: 2024-01-15 15:30 EST
   Currency: USD
   Impact: High
   Time until: 4h 0m
--------------------------------------------------
```

## Troubleshooting

### No Events Found
- The script may not find events if there are no high impact USD events in the next 3 hours
- Check the Forex Factory website manually to verify

### Selenium Issues
- Make sure ChromeDriver is installed and in PATH
- Try updating ChromeDriver to match your Chrome browser version
- On some systems, you may need to install Chrome browser as well

### Requests Issues
- The site may be blocking requests or using JavaScript to load content
- Use the `--selenium-only` flag to bypass this approach

### Dependencies
If you get import errors, install missing packages:
```bash
pip install requests beautifulsoup4 pytz tzlocal lxml selenium
```

## Files

- `run_scraper.py` - Main runner script (recommended)
- `forex_factory_scraper.py` - Requests/BeautifulSoup implementation
- `forex_factory_selenium.py` - Selenium implementation
- `requirements.txt` - Python dependencies
- `README.md` - This file

## Notes

- The script assumes Forex Factory times are in US Eastern timezone
- High impact events are identified by red impact indicators
- The script respects the website's structure and doesn't overload it with requests
- Local timezone detection requires the `tzlocal` package

## Disclaimer

This script is for educational and personal use only. Please respect the website's terms of service and don't abuse their servers with excessive requests.
