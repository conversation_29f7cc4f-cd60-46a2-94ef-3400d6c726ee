#!/usr/bin/env python3
"""
Main runner script for Forex Factory scraper
Tries both BeautifulSoup and Selenium approaches
"""

import sys
import argparse
from datetime import datetime


def try_requests_scraper():
    """Try the requests/BeautifulSoup approach first"""
    try:
        from forex_factory_scraper import ForexFactoryScraper
        print("Trying requests/BeautifulSoup approach...")
        scraper = ForexFactoryScraper()
        events = scraper.run()
        return events
    except Exception as e:
        print(f"Requests approach failed: {e}")
        return None


def try_selenium_scraper():
    """Try the Selenium approach"""
    try:
        from forex_factory_selenium import ForexFactorySeleniumScraper
        print("Trying Selenium approach...")
        scraper = ForexFactorySeleniumScraper(headless=True)
        events = scraper.run()
        return events
    except Exception as e:
        print(f"Selenium approach failed: {e}")
        print("Make sure ChromeDriver is installed: https://chromedriver.chromium.org/")
        return None


def display_events(events):
    """Display the events in a nice format"""
    if not events:
        print("\nNo high impact USD news events found in the next 3 hours.")
        return
    
    print("\n" + "="*70)
    print("HIGH IMPACT USD NEWS - NEXT 3 HOURS")
    print("="*70)
    
    for i, event in enumerate(events, 1):
        local_time = event.get('local_time', event['time'])
        print(f"\n{i}. {event['title']}")
        print(f"   Time: {local_time.strftime('%Y-%m-%d %H:%M %Z')}")
        print(f"   Currency: {event['currency']}")
        print(f"   Impact: {event['impact']}")
        
        # Calculate time until event
        now = datetime.now(local_time.tzinfo)
        time_diff = local_time - now
        hours = int(time_diff.total_seconds() // 3600)
        minutes = int((time_diff.total_seconds() % 3600) // 60)
        
        if time_diff.total_seconds() > 0:
            print(f"   Time until: {hours}h {minutes}m")
        else:
            print(f"   Status: Event has passed")
        
        print("-" * 50)


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Scrape Forex Factory for high impact USD news')
    parser.add_argument('--selenium-only', action='store_true', 
                       help='Use only Selenium approach (skip requests)')
    parser.add_argument('--requests-only', action='store_true',
                       help='Use only requests approach (skip Selenium)')
    
    args = parser.parse_args()
    
    events = None
    
    # Try requests approach first (unless selenium-only is specified)
    if not args.selenium_only:
        events = try_requests_scraper()
    
    # If requests failed or selenium-only is specified, try Selenium
    if (events is None or len(events) == 0) and not args.requests_only:
        print("\nFalling back to Selenium approach...")
        events = try_selenium_scraper()
    
    # Display results
    if events is not None:
        display_events(events)
    else:
        print("\nBoth scraping approaches failed. Please check:")
        print("1. Internet connection")
        print("2. Required dependencies are installed (pip install -r requirements.txt)")
        print("3. For Selenium: ChromeDriver is installed and in PATH")
        sys.exit(1)


if __name__ == "__main__":
    main()
