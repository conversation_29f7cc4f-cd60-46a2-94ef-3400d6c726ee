#!/usr/bin/env python3
"""
Forex Factory Calendar Scraper
Scrapes high impact USD news from forexfactory.com/calendar within the next 3 hours
"""

import requests
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
import pytz
import re
import json
from typing import List, Dict, Optional
import time


class ForexFactoryScraper:
    def __init__(self):
        self.base_url = "https://www.forexfactory.com"
        self.calendar_url = f"{self.base_url}/calendar"
        self.session = requests.Session()
        
        # Set headers to mimic a real browser
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

    def get_local_timezone(self) -> pytz.timezone:
        """Get the local timezone"""
        try:
            import tzlocal
            return tzlocal.get_localzone()
        except ImportError:
            # Fallback to UTC if tzlocal is not available
            print("Warning: tzlocal not available, using UTC. Install with: pip install tzlocal")
            return pytz.UTC

    def parse_time(self, time_str: str, date_str: str) -> Optional[datetime]:
        """Parse time string from Forex Factory format"""
        if not time_str or time_str.strip() == "":
            return None
            
        try:
            # Handle different time formats
            time_str = time_str.strip()
            
            # Remove any extra whitespace and handle AM/PM
            time_str = re.sub(r'\s+', ' ', time_str)
            
            # Try to parse the time
            if 'am' in time_str.lower() or 'pm' in time_str.lower():
                time_obj = datetime.strptime(time_str, '%I:%M%p')
            else:
                # 24-hour format
                time_obj = datetime.strptime(time_str, '%H:%M')
            
            # Combine with date
            if date_str:
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                combined = datetime.combine(date_obj.date(), time_obj.time())
            else:
                # Use today's date
                today = datetime.now().date()
                combined = datetime.combine(today, time_obj.time())
            
            # Assume the time is in EST (Forex Factory's timezone)
            est = pytz.timezone('US/Eastern')
            localized = est.localize(combined)
            
            return localized
            
        except Exception as e:
            print(f"Error parsing time '{time_str}': {e}")
            return None

    def is_usd_related(self, currency: str, title: str) -> bool:
        """Check if the news event is USD related"""
        if not currency:
            return False
            
        currency = currency.upper().strip()
        
        # Direct USD currency
        if currency == 'USD':
            return True
            
        # Check for USD in currency pairs
        if 'USD' in currency:
            return True
            
        # Check title for USD-related keywords
        usd_keywords = ['dollar', 'fed', 'federal reserve', 'fomc', 'us ', 'united states', 'america']
        title_lower = title.lower()
        
        return any(keyword in title_lower for keyword in usd_keywords)

    def is_high_impact(self, impact_element) -> bool:
        """Check if the event is high impact (red icon)"""
        if not impact_element:
            return False
            
        # Look for high impact indicators
        classes = impact_element.get('class', [])
        title = impact_element.get('title', '').lower()
        
        # Check for red/high impact classes or titles
        high_impact_indicators = ['high', 'red', 'impact-3']
        
        return any(indicator in ' '.join(classes).lower() for indicator in high_impact_indicators) or \
               any(indicator in title for indicator in high_impact_indicators)

    def scrape_calendar(self) -> List[Dict]:
        """Scrape the Forex Factory calendar"""
        try:
            print("Fetching Forex Factory calendar...")
            response = self.session.get(self.calendar_url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for calendar table or events
            events = []
            
            # Try to find the calendar table
            calendar_table = soup.find('table', class_='calendar__table')
            if not calendar_table:
                # Try alternative selectors
                calendar_table = soup.find('table', {'id': 'calendar'})
            
            if calendar_table:
                events = self._parse_calendar_table(calendar_table)
            else:
                print("Could not find calendar table. The page structure might have changed.")
                # Try to find events in a different format
                event_rows = soup.find_all('tr', class_=re.compile(r'calendar__row'))
                if event_rows:
                    events = self._parse_event_rows(event_rows)
            
            return events
            
        except requests.RequestException as e:
            print(f"Error fetching calendar: {e}")
            return []
        except Exception as e:
            print(f"Error parsing calendar: {e}")
            return []

    def _parse_calendar_table(self, table) -> List[Dict]:
        """Parse events from calendar table"""
        events = []
        current_date = None
        
        rows = table.find_all('tr')
        
        for row in rows:
            # Check if this is a date row
            date_cell = row.find('td', class_=re.compile(r'calendar__date'))
            if date_cell:
                date_text = date_cell.get_text(strip=True)
                if date_text:
                    try:
                        # Parse date (format might be like "Wed Jan 15")
                        current_date = self._parse_date(date_text)
                    except:
                        pass
                continue
            
            # Check if this is an event row
            time_cell = row.find('td', class_=re.compile(r'calendar__time'))
            if not time_cell:
                continue
                
            event = self._parse_event_row(row, current_date)
            if event:
                events.append(event)
        
        return events

    def _parse_event_rows(self, rows) -> List[Dict]:
        """Parse events from event rows"""
        events = []
        
        for row in rows:
            event = self._parse_event_row(row)
            if event:
                events.append(event)
        
        return events

    def _parse_event_row(self, row, current_date=None) -> Optional[Dict]:
        """Parse a single event row"""
        try:
            # Extract time
            time_cell = row.find('td', class_=re.compile(r'calendar__time'))
            time_str = time_cell.get_text(strip=True) if time_cell else ""
            
            # Extract currency
            currency_cell = row.find('td', class_=re.compile(r'calendar__currency'))
            currency = currency_cell.get_text(strip=True) if currency_cell else ""
            
            # Extract impact
            impact_cell = row.find('td', class_=re.compile(r'calendar__impact'))
            impact_element = impact_cell.find('span') if impact_cell else None
            
            # Extract event title
            event_cell = row.find('td', class_=re.compile(r'calendar__event'))
            title = event_cell.get_text(strip=True) if event_cell else ""
            
            # Check if this is USD-related and high impact
            if not self.is_usd_related(currency, title):
                return None
                
            if not self.is_high_impact(impact_element):
                return None
            
            # Parse the time
            event_time = self.parse_time(time_str, current_date.strftime('%Y-%m-%d') if current_date else None)
            
            return {
                'time': event_time,
                'currency': currency,
                'title': title,
                'impact': 'High',
                'raw_time': time_str
            }
            
        except Exception as e:
            print(f"Error parsing event row: {e}")
            return None

    def _parse_date(self, date_text: str) -> datetime:
        """Parse date from various formats"""
        # This is a simplified parser - you might need to adjust based on actual format
        try:
            # Try different date formats
            for fmt in ['%a %b %d', '%B %d', '%b %d']:
                try:
                    parsed = datetime.strptime(date_text, fmt)
                    # Add current year
                    return parsed.replace(year=datetime.now().year)
                except ValueError:
                    continue
            
            # If no format works, return today
            return datetime.now()
            
        except:
            return datetime.now()

    def filter_next_3_hours(self, events: List[Dict]) -> List[Dict]:
        """Filter events that occur within the next 3 hours"""
        local_tz = self.get_local_timezone()
        now = datetime.now(local_tz)
        three_hours_later = now + timedelta(hours=3)
        
        filtered_events = []
        
        for event in events:
            if event['time']:
                # Convert to local timezone
                local_time = event['time'].astimezone(local_tz)
                
                if now <= local_time <= three_hours_later:
                    event['local_time'] = local_time
                    filtered_events.append(event)
        
        return filtered_events

    def run(self) -> List[Dict]:
        """Main method to run the scraper"""
        print("Starting Forex Factory scraper...")
        
        # Scrape calendar
        all_events = self.scrape_calendar()
        print(f"Found {len(all_events)} total USD high-impact events")
        
        # Filter for next 3 hours
        upcoming_events = self.filter_next_3_hours(all_events)
        print(f"Found {len(upcoming_events)} events in the next 3 hours")
        
        return upcoming_events


def main():
    """Main function"""
    scraper = ForexFactoryScraper()
    events = scraper.run()
    
    if events:
        print("\n" + "="*60)
        print("HIGH IMPACT USD NEWS - NEXT 3 HOURS")
        print("="*60)
        
        for event in events:
            local_time = event.get('local_time', event['time'])
            print(f"\nTime: {local_time.strftime('%Y-%m-%d %H:%M %Z')}")
            print(f"Currency: {event['currency']}")
            print(f"Event: {event['title']}")
            print(f"Impact: {event['impact']}")
            print("-" * 40)
    else:
        print("\nNo high impact USD news events found in the next 3 hours.")


if __name__ == "__main__":
    main()
